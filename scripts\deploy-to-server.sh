#!/bin/bash

# 一键部署脚本 - 会通材质管理系统
# 用于在阿里云服务器上快速部署应用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="huitong-material"
PROJECT_DIR="/opt/${PROJECT_NAME}"
GITHUB_REPO="https://github.com/your-username/${PROJECT_NAME}.git"  # 请替换为实际仓库地址
WEBHOOK_PORT=9000
APP_PORT=3001

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

success() {
    echo -e "${PURPLE}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        warn "检测到 root 用户，建议使用普通用户运行此脚本"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检查系统要求
check_system() {
    info "检查系统要求..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        error "不支持的操作系统"
    fi
    
    # 检查内存
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $MEMORY_GB -lt 2 ]]; then
        warn "内存不足 2GB，可能影响性能"
    fi
    
    # 检查磁盘空间
    DISK_SPACE=$(df / | awk 'NR==2{print $4}')
    if [[ $DISK_SPACE -lt 20971520 ]]; then  # 20GB in KB
        warn "磁盘空间不足 20GB，可能影响运行"
    fi
    
    log "✅ 系统检查完成"
}

# 安装必要工具
install_dependencies() {
    info "安装必要工具..."
    
    # 更新包管理器
    if command -v apt &> /dev/null; then
        sudo apt update
        PACKAGE_MANAGER="apt"
    elif command -v yum &> /dev/null; then
        sudo yum update -y
        PACKAGE_MANAGER="yum"
    else
        error "不支持的包管理器"
    fi
    
    # 安装基础工具
    if [[ $PACKAGE_MANAGER == "apt" ]]; then
        sudo apt install -y curl wget git unzip
    else
        sudo yum install -y curl wget git unzip
    fi
    
    # 安装 Docker
    if ! command -v docker &> /dev/null; then
        info "安装 Docker..."
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh
        sudo usermod -aG docker $USER
        rm get-docker.sh
        log "✅ Docker 安装完成"
    else
        log "✅ Docker 已安装"
    fi
    
    # 安装 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        info "安装 Docker Compose..."
        sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        sudo chmod +x /usr/local/bin/docker-compose
        log "✅ Docker Compose 安装完成"
    else
        log "✅ Docker Compose 已安装"
    fi
    
    # 安装 Node.js
    if ! command -v node &> /dev/null; then
        info "安装 Node.js..."
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        if [[ $PACKAGE_MANAGER == "apt" ]]; then
            sudo apt-get install -y nodejs
        else
            sudo yum install -y nodejs npm
        fi
        log "✅ Node.js 安装完成"
    else
        log "✅ Node.js 已安装"
    fi
    
    # 安装 PM2
    if ! command -v pm2 &> /dev/null; then
        info "安装 PM2..."
        sudo npm install -g pm2
        log "✅ PM2 安装完成"
    else
        log "✅ PM2 已安装"
    fi
}

# 克隆项目
clone_project() {
    info "克隆项目..."
    
    # 创建项目目录
    if [[ ! -d "$PROJECT_DIR" ]]; then
        sudo mkdir -p "$PROJECT_DIR"
        sudo chown $USER:$USER "$PROJECT_DIR"
    fi
    
    # 克隆或更新项目
    if [[ -d "$PROJECT_DIR/.git" ]]; then
        info "更新现有项目..."
        cd "$PROJECT_DIR"
        git pull origin main
    else
        info "克隆新项目..."
        git clone "$GITHUB_REPO" "$PROJECT_DIR"
        cd "$PROJECT_DIR"
    fi
    
    # 设置脚本权限
    chmod +x scripts/*.sh
    
    log "✅ 项目克隆完成"
}

# 配置环境文件
setup_environment() {
    info "配置环境文件..."
    
    cd "$PROJECT_DIR"
    
    # 检查是否存在 .env.production
    if [[ ! -f .env.production ]]; then
        warn ".env.production 文件不存在"
        echo "请手动创建 .env.production 文件，或从以下模板复制："
        echo "----------------------------------------"
        cat << 'EOF'
# 会通材质管理系统 - 生产环境配置
NODE_ENV=production
PORT=3001
APP_NAME="会通材质管理系统"
APP_VERSION=1.0.0

# 数据库配置 (PostgreSQL)
POSTGRES_HOST=db
POSTGRES_PORT=5432
POSTGRES_DB=huitong_material
POSTGRES_USER=user
POSTGRES_PASSWORD=your_secure_password
DATABASE_URL="**********************************************/huitong_material"

# Redis配置
REDIS_HOST=huitong-redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_URL="redis://:your_redis_password@huitong-redis:6379"

# JWT配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 其他配置...
EOF
        echo "----------------------------------------"
        read -p "按 Enter 继续，或 Ctrl+C 退出配置环境文件..."
    else
        log "✅ 环境文件已存在"
    fi
    
    # 设置文件权限
    if [[ -f .env.production ]]; then
        chmod 600 .env.production
    fi
}

# 安装项目依赖
install_project_dependencies() {
    info "安装项目依赖..."
    
    cd "$PROJECT_DIR"
    
    # 安装 Node.js 依赖
    if [[ -f package-lock.json ]]; then
        npm ci --production
    else
        npm install --production
    fi
    
    log "✅ 项目依赖安装完成"
}

# 构建和启动服务
deploy_services() {
    info "构建和启动服务..."
    
    cd "$PROJECT_DIR"
    
    # 停止现有服务
    if [[ -f docker-compose.prod.yml ]]; then
        docker-compose -f docker-compose.prod.yml down || true
    fi
    
    # 构建和启动服务
    if [[ -f docker-compose.prod.yml ]]; then
        docker-compose -f docker-compose.prod.yml up -d --build
    else
        warn "docker-compose.prod.yml 不存在，尝试使用默认配置"
        docker-compose up -d --build
    fi
    
    # 等待服务启动
    sleep 10
    
    log "✅ 服务启动完成"
}

# 配置 Webhook
setup_webhook() {
    info "配置 Webhook 服务..."
    
    cd "$PROJECT_DIR"
    
    # 停止现有 Webhook
    pm2 delete huitong-webhook 2>/dev/null || true
    
    # 启动 Webhook 服务
    if [[ -f scripts/webhook-server.js ]]; then
        pm2 start scripts/webhook-server.js --name "huitong-webhook"
        pm2 save
        
        # 设置开机自启
        pm2 startup | tail -1 | sudo bash || true
        
        log "✅ Webhook 服务配置完成"
    else
        warn "Webhook 服务器脚本不存在，跳过配置"
    fi
}

# 配置防火墙
setup_firewall() {
    info "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        # 开放必要端口
        sudo ufw allow 22/tcp    # SSH
        sudo ufw allow 80/tcp    # HTTP
        sudo ufw allow 443/tcp   # HTTPS
        sudo ufw allow $APP_PORT/tcp      # 应用端口
        sudo ufw allow $WEBHOOK_PORT/tcp  # Webhook 端口
        
        # 启用防火墙（如果未启用）
        sudo ufw --force enable
        
        log "✅ 防火墙配置完成"
    else
        warn "UFW 未安装，请手动配置防火墙"
    fi
}

# 验证部署
verify_deployment() {
    info "验证部署..."
    
    cd "$PROJECT_DIR"
    
    # 检查 Docker 容器状态
    echo "Docker 容器状态："
    if [[ -f docker-compose.prod.yml ]]; then
        docker-compose -f docker-compose.prod.yml ps
    else
        docker-compose ps
    fi
    
    # 检查应用健康状态
    sleep 5
    if curl -f http://localhost:$APP_PORT/health &> /dev/null; then
        success "✅ 应用健康检查通过"
    else
        warn "⚠️ 应用健康检查失败，请检查日志"
    fi
    
    # 检查 Webhook 服务
    if pm2 list | grep -q huitong-webhook; then
        success "✅ Webhook 服务运行正常"
    else
        warn "⚠️ Webhook 服务未运行"
    fi
    
    log "✅ 部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    echo ""
    success "🎉 部署完成！"
    echo ""
    echo "📋 部署信息："
    echo "  项目目录: $PROJECT_DIR"
    echo "  应用端口: $APP_PORT"
    echo "  Webhook 端口: $WEBHOOK_PORT"
    echo ""
    echo "🔗 访问地址："
    echo "  应用: http://$(curl -s ifconfig.me):$APP_PORT"
    echo "  健康检查: http://$(curl -s ifconfig.me):$APP_PORT/health"
    echo "  Webhook: http://$(curl -s ifconfig.me):$WEBHOOK_PORT/webhook"
    echo ""
    echo "📊 管理命令："
    echo "  查看应用日志: cd $PROJECT_DIR && docker-compose logs -f"
    echo "  重启应用: cd $PROJECT_DIR && docker-compose restart"
    echo "  查看 Webhook 日志: pm2 logs huitong-webhook"
    echo "  重启 Webhook: pm2 restart huitong-webhook"
    echo ""
    echo "📚 相关文档："
    echo "  服务器部署指南: $PROJECT_DIR/SERVER-DEPLOY.md"
    echo "  快速设置指南: $PROJECT_DIR/QUICK-SETUP.md"
    echo "  自动化完成文档: $PROJECT_DIR/AUTOMATION-COMPLETE.md"
    echo ""
}

# 主函数
main() {
    echo ""
    success "🚀 开始部署会通材质管理系统到服务器"
    echo ""
    
    # 检查用户权限
    check_root
    
    # 系统检查
    check_system
    
    # 安装依赖
    install_dependencies
    
    # 克隆项目
    clone_project
    
    # 配置环境
    setup_environment
    
    # 安装项目依赖
    install_project_dependencies
    
    # 部署服务
    deploy_services
    
    # 配置 Webhook
    setup_webhook
    
    # 配置防火墙
    setup_firewall
    
    # 验证部署
    verify_deployment
    
    # 显示部署信息
    show_deployment_info
    
    echo ""
    success "🎊 恭喜！会通材质管理系统已成功部署到服务器！"
    echo ""
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi