# 使用官方 Node.js 20 Alpine 镜像作为基础
# Alpine 镜像体积小，更安全
FROM node:20-alpine AS base

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# --- 依赖安装阶段 ---
FROM base AS deps

# 安装生产环境依赖
# --omit=dev 替代 --production，--ignore-scripts 跳过 prepare 脚本避免 husky 错误
RUN npm install --omit=dev --ignore-scripts

# --- 构建阶段 ---
FROM base AS builder

# 安装所有依赖（包括开发依赖，构建需要）
RUN npm install --ignore-scripts
COPY . .

# 生成 Prisma 客户端并构建前端应用
RUN npm run build

# --- 生产镜像阶段 ---
FROM base AS runner

# 设置生产环境变量
ENV NODE_ENV=production

# 从构建器阶段复制必要的产物
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/backend ./backend
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/package.json ./

# 暴露应用端口 (来自 .env.production)
EXPOSE 3001

# 定义容器启动时运行的命令
CMD ["npm", "start"]
