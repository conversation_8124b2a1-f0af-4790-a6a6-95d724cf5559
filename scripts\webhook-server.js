#!/usr/bin/env node

/**
 * GitHub Webhook 服务器
 * 接收GitHub推送事件并自动拉取最新代码
 */

import express from 'express';
import crypto from 'crypto';
import { execSync } from 'child_process';
import { readFileSync } from 'fs';
import path from 'path';

const app = express();
const PORT = process.env.WEBHOOK_PORT || 9000;
const SECRET = process.env.WEBHOOK_SECRET || 'your-webhook-secret';
const PROJECT_DIR = process.env.PROJECT_DIR || '/opt/huitong-material';

// 中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 日志函数
function log(message, level = 'INFO') {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${level}] ${message}`);
}

// 验证GitHub签名
function verifySignature(payload, signature) {
    const hmac = crypto.createHmac('sha256', SECRET);
    const digest = 'sha256=' + hmac.update(payload).digest('hex');
    return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(digest));
}

// 执行命令
function execCommand(command, cwd = PROJECT_DIR) {
    try {
        const result = execSync(command, {
            cwd,
            encoding: 'utf8',
            stdio: 'pipe'
        });
        return { success: true, output: result.trim() };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

// 自动拉取和部署
async function autoDeploy(branch = 'main') {
    log(`开始自动部署分支: ${branch}`);
    
    try {
        // 执行服务器拉取脚本
        const scriptPath = path.join(PROJECT_DIR, 'scripts', 'server-pull.sh');
        const result = execCommand(`chmod +x ${scriptPath} && ${scriptPath} ${branch}`);
        
        if (result.success) {
            log('自动部署成功');
            return { success: true, message: '部署成功' };
        } else {
            log(`自动部署失败: ${result.error}`, 'ERROR');
            return { success: false, message: result.error };
        }
    } catch (error) {
        log(`自动部署异常: ${error.message}`, 'ERROR');
        return { success: false, message: error.message };
    }
}

// 发送通知
async function sendNotification(event, result) {
    // 这里可以集成钉钉、企业微信、Slack等通知
    const message = {
        event: event.type,
        repository: event.repository?.full_name,
        branch: event.ref?.replace('refs/heads/', ''),
        commit: event.head_commit?.id?.substring(0, 8),
        author: event.head_commit?.author?.name,
        message: event.head_commit?.message,
        result: result.success ? '成功' : '失败',
        error: result.message
    };
    
    log(`部署通知: ${JSON.stringify(message)}`);
    
    // TODO: 实现具体的通知逻辑
    // 例如：发送到钉钉群、企业微信、邮件等
}

// 健康检查端点
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// GitHub Webhook端点
app.post('/webhook', async (req, res) => {
    const signature = req.headers['x-hub-signature-256'];
    const event = req.headers['x-github-event'];
    const payload = JSON.stringify(req.body);
    
    log(`收到GitHub事件: ${event}`);
    
    // 验证签名
    if (!verifySignature(payload, signature)) {
        log('签名验证失败', 'ERROR');
        return res.status(401).json({ error: '签名验证失败' });
    }
    
    // 只处理push事件
    if (event !== 'push') {
        log(`忽略事件类型: ${event}`);
        return res.json({ message: '事件已忽略' });
    }
    
    const pushEvent = req.body;
    const branch = pushEvent.ref?.replace('refs/heads/', '');
    
    // 只处理main和develop分支
    if (!['main', 'develop'].includes(branch)) {
        log(`忽略分支: ${branch}`);
        return res.json({ message: '分支已忽略' });
    }
    
    log(`处理推送事件: ${pushEvent.repository.full_name}:${branch}`);
    log(`提交: ${pushEvent.head_commit.id.substring(0, 8)} by ${pushEvent.head_commit.author.name}`);
    log(`消息: ${pushEvent.head_commit.message}`);
    
    // 异步执行部署
    setImmediate(async () => {
        const result = await autoDeploy(branch);
        await sendNotification(pushEvent, result);
    });
    
    res.json({ 
        message: '部署任务已启动',
        branch,
        commit: pushEvent.head_commit.id.substring(0, 8)
    });
});

// 手动部署端点
app.post('/deploy', async (req, res) => {
    const { branch = 'main', token } = req.body;
    
    // 简单的token验证
    if (token !== process.env.DEPLOY_TOKEN) {
        return res.status(401).json({ error: '无效的部署令牌' });
    }
    
    log(`手动部署请求: ${branch}`);
    
    const result = await autoDeploy(branch);
    
    res.json({
        success: result.success,
        message: result.message,
        branch,
        timestamp: new Date().toISOString()
    });
});

// 部署状态端点
app.get('/status', (req, res) => {
    try {
        // 检查服务状态
        const composeResult = execCommand('docker-compose ps --format json');
        const gitResult = execCommand('git log -1 --format="%H|%an|%ad|%s"');
        
        let services = [];
        if (composeResult.success) {
            try {
                services = JSON.parse(composeResult.output);
            } catch (e) {
                services = [];
            }
        }
        
        let gitInfo = {};
        if (gitResult.success) {
            const [hash, author, date, message] = gitResult.output.split('|');
            gitInfo = { hash: hash.substring(0, 8), author, date, message };
        }
        
        res.json({
            services,
            git: gitInfo,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 错误处理
app.use((error, req, res, next) => {
    log(`服务器错误: ${error.message}`, 'ERROR');
    res.status(500).json({ error: '内部服务器错误' });
});

// 启动服务器
app.listen(PORT, () => {
    log(`Webhook服务器启动成功，端口: ${PORT}`);
    log(`项目目录: ${PROJECT_DIR}`);
    log(`Webhook端点: http://localhost:${PORT}/webhook`);
    log(`健康检查: http://localhost:${PORT}/health`);
});

// 优雅关闭
process.on('SIGTERM', () => {
    log('收到SIGTERM信号，正在关闭服务器...');
    process.exit(0);
});

process.on('SIGINT', () => {
    log('收到SIGINT信号，正在关闭服务器...');
    process.exit(0);
});