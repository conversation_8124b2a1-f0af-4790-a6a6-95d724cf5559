{"mcpServers": {"github-remote": {"type": "http", "url": "https://api.githubcopilot.com/mcp/", "description": "GitHub's official remote MCP server (OAuth-based, no token required)"}, "github-local": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_PAT}"}, "description": "Local GitHub MCP server (requires GitHub Personal Access Token)"}}, "environment": {"GITHUB_PAT": {"description": "GitHub Personal Access Token for local MCP server", "required": false, "note": "Only needed if using github-local server instead of github-remote"}}}