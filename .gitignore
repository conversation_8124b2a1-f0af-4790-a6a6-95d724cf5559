# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
# dist - 临时允许提交构建文件用于部署
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Database
backend/database.sqlite

# Uploads
# backend/uploads/

# Environment variables
.env
.env.local
.env.production
.env.mcp
!.env.example
!.env.production.example
!.env.mcp.example
