#!/bin/bash

# 服务器环境检查脚本
# 用于验证阿里云服务器是否满足部署要求

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# 检查函数
check_command() {
    if command -v $1 &> /dev/null; then
        log "✅ $1 已安装"
        $1 --version 2>/dev/null || $1 -v 2>/dev/null || echo "版本信息不可用"
        return 0
    else
        error "❌ $1 未安装"
        return 1
    fi
}

check_port() {
    if netstat -tuln | grep -q ":$1 "; then
        warn "⚠️ 端口 $1 已被占用"
        netstat -tuln | grep ":$1 "
    else
        log "✅ 端口 $1 可用"
    fi
}

check_directory() {
    if [ -d "$1" ]; then
        log "✅ 目录 $1 存在"
        ls -la "$1" | head -5
    else
        warn "⚠️ 目录 $1 不存在"
    fi
}

# 开始检查
log "🔍 开始服务器环境检查..."

# 1. 系统信息
info "📋 系统信息:"
uname -a
cat /etc/os-release | head -5

# 2. 检查必要的命令
info "🛠️ 检查必要工具:"
REQUIRED_COMMANDS=("git" "docker" "docker-compose" "node" "npm")
MISSING_COMMANDS=()

for cmd in "${REQUIRED_COMMANDS[@]}"; do
    if ! check_command $cmd; then
        MISSING_COMMANDS+=($cmd)
    fi
done

# 3. 检查端口
info "🔌 检查端口占用:"
PORTS=(3001 5432 6379 9000)
for port in "${PORTS[@]}"; do
    check_port $port
done

# 4. 检查目录
info "📁 检查项目目录:"
PROJECT_DIRS=("/opt/huitong-material" "/var/log" "/tmp")
for dir in "${PROJECT_DIRS[@]}"; do
    check_directory $dir
done

# 5. 检查磁盘空间
info "💾 磁盘空间检查:"
df -h

# 6. 检查内存
info "🧠 内存使用情况:"
free -h

# 7. 检查网络连接
info "🌐 网络连接检查:"
if ping -c 3 github.com &> /dev/null; then
    log "✅ 网络连接正常 (GitHub可达)"
else
    error "❌ 网络连接异常 (GitHub不可达)"
fi

# 8. Docker 状态检查
if command -v docker &> /dev/null; then
    info "🐳 Docker 状态:"
    sudo docker info | grep -E "(Server Version|Storage Driver|Kernel Version)" || true
    sudo docker ps -a | head -5 || true
fi

# 总结
echo ""
log "📊 环境检查完成"

if [ ${#MISSING_COMMANDS[@]} -eq 0 ]; then
    log "🎉 所有必要工具都已安装！"
    log "💡 下一步: 运行部署脚本"
    echo ""
    echo "建议执行的命令:"
    echo "1. cd /opt/huitong-material"
    echo "2. ./scripts/server-pull.sh main"
    echo "3. npm run webhook:start"
else
    error "❌ 缺少以下工具: ${MISSING_COMMANDS[*]}"
    echo ""
    echo "安装命令:"
    for cmd in "${MISSING_COMMANDS[@]}"; do
        case $cmd in
            "docker")
                echo "curl -fsSL https://get.docker.com -o get-docker.sh && sudo sh get-docker.sh"
                ;;
            "docker-compose")
                echo "sudo curl -L \"https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose"
                echo "sudo chmod +x /usr/local/bin/docker-compose"
                ;;
            "node"|"npm")
                echo "curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -"
                echo "sudo apt-get install -y nodejs"
                ;;
            "git")
                echo "sudo apt-get update && sudo apt-get install -y git"
                ;;
        esac
    done
fi

echo ""
log "🔗 相关文档:"
echo "- 快速设置指南: QUICK-SETUP.md"
echo "- 详细部署文档: AUTO-DEPLOY.md"
echo "- 故障排除: AUTOMATION-COMPLETE.md"