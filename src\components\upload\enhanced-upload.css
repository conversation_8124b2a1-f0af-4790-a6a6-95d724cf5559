/* 增强版上传组件样式 */
.enhanced-upload {
  width: 100%;
}

/* 上传区域基础样式 */
.enhanced-upload__area {
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  background-color: var(--color-bg-overlay);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* 当有文件信息时，移除上传区域的边框避免双重边框 */
.enhanced-upload__area:has(.enhanced-upload__file-info) {
  border: none;
  background-color: transparent;
  padding: 0;
}

.enhanced-upload__area:hover {
  background-color: var(--color-bg-hover);
}

.enhanced-upload__area--drag-over {
  border-color: var(--color-primary);
  background-color: var(--color-primary-light);
  animation: uploadPulse 0.6s ease-in-out;
}

.enhanced-upload__area--disabled {
  opacity: var(--opacity-disabled);
  cursor: not-allowed;
  pointer-events: none;
}

/* 尺寸变体 */
.enhanced-upload__area--small {
  height: var(--size-upload-small);
}

.enhanced-upload__area--medium {
  height: var(--size-upload-medium);
}

.enhanced-upload__area--large {
  height: var(--size-upload-large);
}

/* 图片上传特殊尺寸 */
.enhanced-upload__area--image {
  width: var(--size-upload-image);
  height: var(--size-upload-image);
}

.enhanced-upload__area--image.enhanced-upload__area--small {
  width: var(--size-upload-image-small);
  height: var(--size-upload-image-small);
}

.enhanced-upload__area--image.enhanced-upload__area--large {
  width: var(--size-upload-image-large);
  height: var(--size-upload-image-large);
}

/* 纹理上传特殊尺寸 */
.enhanced-upload__area--texture {
  height: var(--size-upload-texture);
}

/* 动态内边距 */
.enhanced-upload__area:has(.enhanced-upload__preview-container),
.enhanced-upload__area:has(.enhanced-upload__texture-preview) {
  padding: 0;
}

.enhanced-upload__area:has(.enhanced-upload__placeholder),
.enhanced-upload__area:has(.enhanced-upload__file-info) {
  padding: var(--spacing-base);
}

/* 占位符样式 */
.enhanced-upload__placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  width: 100%;
  text-align: center;
  color: var(--color-content-regular);
}

.enhanced-upload__placeholder-text {
  font-size: var(--font-size-sm);
}

/* 增强上传图标样式 */
.enhanced-upload__icon {
  color: var(--color-content-mute);
}

.enhanced-upload__icon--image {
  width: var(--spacing-xl);
  height: var(--spacing-xl);
}

.enhanced-upload__icon--texture,
.enhanced-upload__icon--model {
  width: var(--spacing-lg);
  height: var(--spacing-lg);
}

.enhanced-upload__format-hint {
  opacity: var(--opacity-subtle);
  font-size: var(--font-size-sm);
  margin: 0;
}

/* 预览容器样式 */
.enhanced-upload__preview-container,
.enhanced-upload__texture-preview {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.enhanced-upload__preview-image,
.enhanced-upload__texture-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* 文件信息显示样式 - 优化颜色和可读性 */
.enhanced-upload__file-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--color-bg-overlay);
  border-radius: var(--radius-base);
  padding: var(--spacing-sm);
  height: var(--input-height-medium);
  width: 100%;
  position: relative;
  box-sizing: border-box;
}

.enhanced-upload__file-icon {
  display: none;
}

.enhanced-upload__file-details {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}

.enhanced-upload__file-name {
  color: var(--color-content-regular);
  font-size: var(--font-size-base);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0;
  line-height: var(--line-height-base);
}

.enhanced-upload__file-meta {
  display: none;
}

.enhanced-upload__file-format {
  font-weight: var(--font-weight-medium);
  color: var(--color-brand);
  text-transform: uppercase;
}

.enhanced-upload__file-separator {
  color: var(--color-content-mute);
  opacity: var(--opacity-muted);
}

.enhanced-upload__file-size {
  color: var(--color-content-secondary);
  opacity: var(--opacity-secondary);
}

/* 移除按钮样式 */
.enhanced-upload__remove-button {
  position: static;
  width: var(--button-height-small);
  height: var(--button-height-small);
  border: none;
  border-radius: var(--radius-base);
  background: var(--color-danger);
  color: var(--color-content-white);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  transition: var(--transition-base);
  flex-shrink: 0;
}

/* 预览容器中的IconButton删除按钮 - 绝对定位到右上角 */
.enhanced-upload__preview-container .icon-button,
.enhanced-upload__texture-preview .icon-button {
  position: absolute;
  top: 4px;
  right: 4px;
  z-index: 10;
}

/* 图片预览和纹理预览中的X按钮背景样式 */
.enhanced-upload__preview-container .icon-button,
.enhanced-upload__texture-preview .icon-button {
  background: rgba(0, 0, 0, 0.7);
  border-radius: var(--radius-s);
}

/* 图片预览和纹理预览中的按钮hover效果 */
.enhanced-upload__preview-container .icon-button:hover:not(:disabled),
.enhanced-upload__texture-preview .icon-button:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.9);
  transition: all 0.2s ease;
}

/* 文件信息容器中的IconButton删除按钮 */
.enhanced-upload__file-info .icon-button {
  position: absolute;
  top: 50%;
  right: var(--spacing-sm);
  transform: translateY(-50%);
}

/* 隐藏的文件输入 */
.enhanced-upload__input {
  display: none;
}

/* 模态框内容样式 */
.enhanced-upload__modal-content {
  padding: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.enhanced-upload__modal-content .enhanced-upload {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.enhanced-upload__modal-content .enhanced-upload__area {
  flex: 1;
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-upload__area--large {
    height: var(--size-upload-medium);
  }
  
  .enhanced-upload__area--image.enhanced-upload__area--large {
    width: var(--size-upload-image);
    height: var(--size-upload-image);
  }
}

/* 动画效果 */
@keyframes uploadPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

/* 无障碍支持 */
.enhanced-upload__area:focus-visible,
.enhanced-upload__remove-button:focus-visible {
  outline: var(--outline-width) solid var(--color-primary);
  outline-offset: var(--outline-offset);
}