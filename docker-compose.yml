services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: huitong_app
    restart: always
    env_file:
      - .env.production
    ports:
      - "3001:3001"
    command: sh -c "npm run db:push && npm start"
    depends_on:
      - db
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - /opt/huitong-material/backups:/opt/huitong-material/backups
    networks:
      - huitong_network

  db:
    image: postgres:14-alpine
    container_name: huitong_db
    restart: always
    env_file:
      - .env.production
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - huitong_network

  redis:
    image: redis:7-alpine
    container_name: huitong_redis
    restart: always
    env_file:
      - .env.production
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - huitong_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  huitong_network:
    driver: bridge
