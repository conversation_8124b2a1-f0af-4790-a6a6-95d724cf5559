# 🎨 会通材质管理系统

一个现代化的 3D 材质管理和预览系统，支持材质上传、预览、管理和自动化部署。

## 🚀 快速开始

### 本地开发

```bash
# 1. 克隆项目
git clone https://github.com/your-username/huitong-material.git
cd huitong-material

# 2. 安装依赖
npm install

# 3. 配置环境
cp .env.example .env.development
# 编辑 .env.development 配置数据库等信息

# 4. 启动开发服务器
npm run dev
```

### 生产部署

#### 方式一：阿里云一键部署（推荐）

```bash
# 在服务器上运行
curl -fsSL https://raw.githubusercontent.com/your-username/huitong-material/main/scripts/deploy-to-server.sh -o deploy.sh
chmod +x deploy.sh
./deploy.sh
```

#### 方式二：Docker 部署

```bash
# 1. 配置生产环境
cp .env.example .env.production
# 编辑 .env.production

# 2. 启动服务
npm run deploy:production
```

## 📋 可用命令

### 开发命令
```bash
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run preview      # 预览构建结果
```

### 部署命令
```bash
npm run push:msg "提交信息"    # 自动提交并推送
npm run deploy:production     # 启动生产环境
npm run deploy:restart        # 重启生产环境
npm run server:deploy         # 一键部署到服务器
```

### 管理命令
```bash
npm run logs:all      # 查看所有日志
npm run webhook:start # 启动自动部署服务
```

## 🔧 环境配置

创建 `.env.production` 文件：

```env
NODE_ENV=production
PORT=3001
DATABASE_URL="postgresql://user:password@localhost:5432/huitong_material"
JWT_SECRET=your_jwt_secret
```

## 🌐 GitHub Webhook 配置

1. 进入 GitHub 仓库 Settings → Webhooks
2. 添加 Webhook：
   - URL: `http://your-server:9000/webhook`
   - Content type: `application/json`
   - Events: `push`

## 📊 功能特性

- ✅ 3D 材质预览和管理
- ✅ 文件上传和存储
- ✅ 自动化部署流程
- ✅ Docker 容器化
- ✅ 数据库集成
- ✅ 响应式设计

## 🛠️ 技术栈

- **前端**: React + TypeScript + Three.js + Vite
- **后端**: Node.js + Express + Prisma
- **数据库**: PostgreSQL + Redis
- **部署**: Docker + PM2 + GitHub Actions

## 📞 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   sudo netstat -tulpn | grep :3001
   sudo kill -9 <PID>
   ```

2. **数据库连接失败**
   ```bash
   npm run logs:all
   npm run deploy:restart
   ```

3. **自动部署不工作**
   ```bash
   pm2 logs huitong-webhook
   pm2 restart huitong-webhook
   ```

## 📄 许可证

MIT License

---

🎉 **快速部署，简单管理！**