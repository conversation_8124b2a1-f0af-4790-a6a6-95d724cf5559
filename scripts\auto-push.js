#!/usr/bin/env node

/**
 * 自动推送脚本
 * 自动提交本地修改并推送到远程仓库
 */

import { execSync } from 'child_process';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, options = {}) {
  try {
    const result = execSync(command, {
      cwd: projectRoot,
      encoding: 'utf8',
      stdio: 'pipe',
      ...options
    });
    return result.trim();
  } catch (error) {
    throw new Error(`命令执行失败: ${command}\n错误: ${error.message}`);
  }
}

function getCommitMessage() {
  const args = process.argv.slice(2);
  
  if (args.length > 0) {
    return args.join(' ');
  }
  
  // 自动生成提交信息
  const now = new Date();
  const timestamp = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
  
  return `自动提交 - ${timestamp}`;
}

function checkGitStatus() {
  try {
    const status = execCommand('git status --porcelain');
    return status.length > 0;
  } catch (error) {
    log('❌ 检查Git状态失败', 'red');
    throw error;
  }
}

function getCurrentBranch() {
  try {
    return execCommand('git branch --show-current');
  } catch (error) {
    log('❌ 获取当前分支失败', 'red');
    throw error;
  }
}

function checkRemoteConnection() {
  try {
    execCommand('git remote -v');
    return true;
  } catch (error) {
    log('❌ 检查远程仓库连接失败', 'red');
    return false;
  }
}

async function autoPush() {
  log('🚀 开始自动推送流程...', 'cyan');
  
  try {
    // 检查是否在Git仓库中
    try {
      execCommand('git rev-parse --git-dir');
    } catch (error) {
      log('❌ 当前目录不是Git仓库', 'red');
      process.exit(1);
    }
    
    // 检查远程仓库连接
    if (!checkRemoteConnection()) {
      log('❌ 远程仓库连接失败，请检查网络或仓库配置', 'red');
      process.exit(1);
    }
    
    // 获取当前分支
    const currentBranch = getCurrentBranch();
    log(`📍 当前分支: ${currentBranch}`, 'blue');
    
    // 检查是否有未提交的修改
    if (!checkGitStatus()) {
      log('✅ 没有需要提交的修改', 'green');
      
      // 尝试推送（可能有本地提交未推送）
      try {
        log('🔄 检查是否有本地提交需要推送...', 'yellow');
        const unpushedCommits = execCommand(`git log origin/${currentBranch}..HEAD --oneline`);
        
        if (unpushedCommits) {
          log('📤 发现未推送的提交，正在推送...', 'yellow');
          execCommand(`git push origin ${currentBranch}`);
          log('✅ 推送完成', 'green');
        } else {
          log('✅ 所有提交都已推送', 'green');
        }
      } catch (error) {
        log('⚠️ 推送检查失败，可能是首次推送', 'yellow');
      }
      
      process.exit(0);
    }
    
    log('📝 发现未提交的修改', 'yellow');
    
    // 显示修改状态
    const status = execCommand('git status --short');
    log('修改文件:', 'yellow');
    console.log(status);
    
    // 添加所有修改
    log('📦 添加所有修改到暂存区...', 'yellow');
    execCommand('git add .');
    
    // 获取提交信息
    const commitMessage = getCommitMessage();
    log(`💬 提交信息: ${commitMessage}`, 'blue');
    
    // 提交修改
    log('💾 提交修改...', 'yellow');
    execCommand(`git commit -m "${commitMessage}"`);
    
    // 推送到远程仓库
    log('📤 推送到远程仓库...', 'yellow');
    execCommand(`git push origin ${currentBranch}`);
    
    log('✅ 自动推送完成！', 'green');
    log(`🌐 分支 ${currentBranch} 已成功推送到远程仓库`, 'green');
    
    // 显示最新提交信息
    const latestCommit = execCommand('git log -1 --oneline');
    log(`📋 最新提交: ${latestCommit}`, 'blue');
    
  } catch (error) {
    log(`❌ 自动推送失败: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  autoPush();
}

export { autoPush };