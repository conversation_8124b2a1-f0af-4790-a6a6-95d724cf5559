name: 自动构建检查

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'

jobs:
  # 代码质量检查和构建
  build-and-check:
    runs-on: ubuntu-latest
    name: 构建和检查
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 代码格式检查
      run: npm run lint
      
    - name: 构建检查
      run: npm run build

  # 部署通知
  deploy-notification:
    runs-on: ubuntu-latest
    needs: build-and-check
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    name: 部署通知
    
    steps:
    - name: 部署成功通知
      run: |
        echo "✅ 代码已成功推送到 main 分支"
        echo "🚀 可以使用 Webhook 或手动部署到服务器"
        echo "📝 部署命令: npm run server:deploy"