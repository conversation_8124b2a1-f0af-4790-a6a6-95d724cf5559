import React from 'react';
import './upload-model-modal.css';
import { FILE_VALIDATION_CONFIGS } from '../../utils/fileUpload';
import { Modal } from '../modal/modal';
import { ModelUpload } from '../upload';

interface UploadModelModalProps {
  visible: boolean;
  onClose: () => void;
  onUpload: (file: File) => void;
}

export const UploadModelModal: React.FC<UploadModelModalProps> = ({ visible, onClose, onUpload }) => {
  if (!visible) return null;

  const handleFileSelect = (file: File) => {
    onUpload(file);
  };

  const handleError = (error: string) => {
    alert(error); // 保持原有的alert行为
  };

  return (
    <Modal
      visible={visible}
      title="上传模型"
      onClose={onClose}
      size="medium"
    >
      <div className="upload-model-modal-content">
        <ModelUpload
          onFileSelect={handleFileSelect}
          validationOptions={FILE_VALIDATION_CONFIGS.MODEL}
          onError={handleError}
          accept=".glb,.gltf"
          placeholder="点击或拖拽模型文件到此处上传"
          formatHint="支持格式：GLB、GLTF"
          size="large"
        />
      </div>
    </Modal>
  );
};
